import { useChatStore } from "@renderer/store/chatStore";
import { useUIStore } from "@renderer/store/uiStore";
import { Loader2 } from "lucide-react";
import { useEffect, useRef } from 'react';
import { Button } from "../ui/button";
import { ChatInput } from "./ChatInput";
import { Header } from "./Header";
import { MessageBubble } from "./MessageBubble";


interface ChatAreaProps {
  // selectedModelId 也由 App.tsx 管理，并在发送消息时传递给 store action
  selectedModelId: number;
}

export function ChatArea({
  selectedModelId,
}: ChatAreaProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 从 chatStore 获取状态
  const storeMessages = useChatStore((state) => state.messages);
  const inputText = useChatStore((state) => state.inputText);
  const selectedConversationId = useChatStore((state) => state.selectedConversationId);
  const conversations = useChatStore((state) => state.conversations); // 用于获取当前会话标题
  const isLoadingMessages = useChatStore((state) => state.isLoadingMessages);
  const isStreamingAIMessage = useChatStore((state) => state.isStreamingAIMessage);

  // 从 uiStore 获取状态
  const appIsModelConfigPanelOpen = useUIStore((state) => state.isModelConfigPanelOpen); // 确保欢迎屏幕正确显示

  // 从 chatStore 获取 actions
  const setInputTextInStore = useChatStore((state) => state.setInputText);
  const sendMessageInStore = useChatStore((state) => state.sendMessage);
  const createNewConversationUIInStore = useChatStore((state) => state.createNewConversationUI);
  // const selectConversationInStore = useChatStore((state) => state.selectConversation);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [storeMessages]);

  const currentConversation = selectedConversationId
    ? conversations.find(c => c.id === selectedConversationId)
    : null;
  const currentTitle = currentConversation?.title || '新对话';

  const handleSendMessage = () => {
    sendMessageInStore(selectedModelId); // 将当前选中的模型ID传递给action
  };

  // const handleCloseConversation = () => {
  //   selectConversationInStore(null); // 调用 store action 关闭会话
  // };


  // 欢迎屏幕逻辑
  if (selectedConversationId === null && storeMessages.length === 0 && !appIsModelConfigPanelOpen) {
    return (
      <>
        <Header
          title="新对话"
        />
        <div className="flex-1 flex items-center justify-center bg-[#f9f9f9] dark:bg-[#141414]">
          <div className="text-center max-w-md p-6">
            <h2 className="text-xl font-semibold mb-4">欢迎使用 LiftLoom</h2>
            <p className="text-muted-foreground mb-6 leading-relaxed font-medium">
              LiftLoom 是一款高度可定制的 LLM 聊天客户端应用，旨在提供灵活且智能的 AI 交互体验。
            </p>
            <Button
              className="bg-primary text-white hover:bg-primary/90 h-10 px-5 font-medium rounded-lg shadow-sm"
              onClick={createNewConversationUIInStore}
            >
              开始新对话
            </Button>
          </div>
        </div>
        <ChatInput
          value={inputText}
          onChange={setInputTextInStore}
          onSend={handleSendMessage}
          disabled={isStreamingAIMessage}
        />
      </>
    );
  }

  return (
    <>
      <Header
        title={currentTitle}
      />
      <div className="flex-1 p-5 overflow-y-auto bg-[#f9f9f9] dark:bg-[#141414]">
        {isLoadingMessages ? (
          <div className="flex h-full items-center justify-center">
            <Loader2 className="h-8 w-8 text-muted-foreground animate-spin" />
          </div>
        ) : (
          <div className="w-full space-y-2 px-4 sm:px-6 md:px-8">
            {storeMessages.map(message => (
              <MessageBubble
                key={message.id} // 确保 key 是唯一的，DB ID 是最好的
                isUser={message.isUser}
                content={message.text}
              />
            ))}
            {/* AI流式输出时，最后一条消息如果是AI的且内容为空，可以显示一个加载指示 */}
            {isStreamingAIMessage &&
              storeMessages.length > 0 &&
              !storeMessages[storeMessages.length - 1].isUser &&
              storeMessages[storeMessages.length - 1].text === '' && (
                <MessageBubble key="ai-typing" isUser={false} content="..." />
              )}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
      <ChatInput
        value={inputText}
        onChange={setInputTextInStore}
        onSend={handleSendMessage}
        disabled={isStreamingAIMessage || isLoadingMessages}
      />
    </>
  );
}